{"name": "devhub-frontend", "version": "0.1.0", "private": true, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@supabase/supabase-js": "^2.57.4", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.8.2", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.13"}}